# Initial Prompt

You are working on the aa_record_ranger_ml_pipeline healthcare document processing system. I need you to create a comprehensive implementation plan for a new feature that enables client-specific summary customization.

**Context and Requirements:**
- **Feature Goal**: Implement tenant-specific summary customization where each client/tenant can have their own summary templates and configurations
- **Key Concept**: Client and tenant are synonymous terms throughout this system
- **Granularity**: Customization should support both tenant-level and sub-tenant level configurations
- **Backward Compatibility**: Current summary implementation must become the default configuration stored in the database

**Reference Documentation:**
- Main user story: `docs/grooming/custom_summaries/summaries_customized_by_client.md`
- Technical analysis: `docs/grooming/custom_summaries/keyword_document_type_relationship_research.md`
- Database schemas:
  - `docs/grooming/custom_summaries/rr.sql` (Record Ranger schema)
  - `docs/grooming/custom_summaries/queue.sql` (Queue management schema)
  - `docs/grooming/custom_summaries/backend.sql` (Primary backend schema - most relevant)

**Deliverable:**
Create a detailed, hierarchical implementation plan structured as a prompt for a coding agent. The plan should:

1. **Be saved as**: `docs/grooming/custom_summaries/implementation_plan.md`
2. **Follow this structure**:
   - Executive summary of the feature
   - High-level architecture changes required
   - Database schema modifications (referencing existing tenant/sub-tenant structures)
   - Backend API changes and new endpoints
   - Service layer modifications
   - Configuration management approach
   - Migration strategy for existing summaries to become default templates
   - Testing strategy (unit tests, integration tests)
   - Deployment considerations

3. **Include specific tasks for**:
   - Database schema updates linking custom summaries to tenant/sub-tenant entities
   - API endpoints for CRUD operations on custom summary configurations
   - Service layer logic for template selection and rendering
   - Migration scripts to convert current summary logic into default database configurations
   - Comprehensive unit test coverage for all new components
   - Integration tests for the complete customization workflow

4. **Technical Requirements**:
   - Maintain HIPAA compliance throughout the implementation
   - Ensure the feature integrates with existing RabbitMQ message processing pipeline
   - Support hierarchical configuration (sub-tenant inherits from tenant unless overridden)
   - Include rollback mechanisms and feature flags for safe deployment

**Format**: Write the plan as a detailed prompt that a coding agent could follow step-by-step, with clear acceptance criteria for each task and explicit references to the existing codebase components that need modification.